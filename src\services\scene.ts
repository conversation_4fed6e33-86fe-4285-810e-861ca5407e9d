import { get, post, del } from './api';

// 角色信息接口
export interface CharacterInfo {
  id: number;
  name: string;
  gender: number; // 0：未知；1：男；2：女
  avatar?: string | null;
  profile: string;
  timbre_type: number; // 0：未设置；1：火山引擎
  timbre?: string | null;
  played: number; // 是否是学员扮演（0：否；1：是）
}

// 指南信息接口
export interface GuideInfo {
  title: string;
  details: string;
}

// 发言信息接口
export interface SpeechInfo {
  id: number;
  cid: number; // 角色ID
  played: number; // 是否是学员扮演（0：否；1：是）
  content: string;
  to_cids?: string | null; // @列表（角色ID列表，用逗号分隔）
  ctime: string; // 发言时间
}

// 场景基本信息响应接口
export interface SceneBasicResponse {
  title: string;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  report?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number; // 练习状态（0：待练习；1：练习中；2：已提交）
  eid: number;
  elid?: number | null;
  tid?: number | null;
  tname?: string | null;
  tavatar?: string | null;
  characters: CharacterInfo[];
  guides: GuideInfo[];
  speeches: SpeechInfo[];
}

// 创建发言请求接口
export interface CreateSpeechRequest {
  elid: number;
  cid: number;
  played: number;
  content: string;
  to_cids?: string;
}

// 创建发言响应接口
export interface CreateSpeechResponse {
  id: number;
  status: number;
  elid: number;
}

// 任务信息接口
export interface JobInfo {
  id: number;
  cid: number; // 角色ID
  status: number; // 任务状态
  sync: number; // 同步标志（0：异步；1：同步）
  ctime: string; // 创建时间
}

// 创建任务响应接口
export interface CreateJobsResponse {
  jobs: JobInfo[];
}

// 获取任务列表响应接口
export interface GetJobsResponse {
  jobs: JobInfo[];
}

// 批量删除发言请求接口
export interface DeleteSpeechBatchRequest {
  from_id: number;
  elid: number;
}

// 批量删除发言响应接口
export interface DeleteSpeechBatchResponse {
  deleted_count: number;
}

/**
 * 获取场景基本信息
 * @param sceneId 场景ID
 * @param classId 班级ID
 * @returns Promise<SceneBasicResponse> 场景信息
 */
export const getSceneData = async (sceneId: number, classId: number): Promise<SceneBasicResponse> => {
  try {
    const response = await get<SceneBasicResponse>(`/scene/${sceneId}?class_id=${classId}`);
    return response;
  } catch (error) {
    console.error('获取场景数据失败:', error);
    throw error;
  }
};

/**
 * 创建学员发言
 * @param request 创建发言请求
 * @returns Promise<CreateSpeechResponse> 创建结果
 */
export const createSpeech = async (request: CreateSpeechRequest): Promise<CreateSpeechResponse> => {
  try {
    const response = await post<CreateSpeechResponse>('/scene/speech', request);
    return response;
  } catch (error) {
    console.error('创建发言失败:', error);
    throw error;
  }
};

/**
 * 为发言创建任务
 * @param speechId 发言ID
 * @param elid 练习情况ID
 * @returns Promise<CreateJobsResponse> 创建的任务列表
 */
export const createSpeechJobs = async (speechId: number, elid: number): Promise<CreateJobsResponse> => {
  try {
    const response = await post<CreateJobsResponse>(`/scene/speech/${speechId}/jobs`, { elid });
    return response;
  } catch (error) {
    console.error('创建任务失败:', error);
    throw error;
  }
};

/**
 * 获取发言的任务列表
 * @param speechId 发言ID
 * @param elid 练习ID
 * @returns Promise<GetJobsResponse> 任务列表
 */
export const getSpeechJobs = async (speechId: number, elid: number): Promise<GetJobsResponse> => {
  try {
    const response = await get<GetJobsResponse>(`/scene/speech/${speechId}/jobs?elid=${elid}`);
    return response;
  } catch (error) {
    console.error('获取任务列表失败:', error);
    throw error;
  }
};

/**
 * 批量删除发言
 * @param request 批量删除请求
 * @returns Promise<DeleteSpeechBatchResponse> 删除结果
 */
export const deleteSpeechBatch = async (request: DeleteSpeechBatchRequest): Promise<DeleteSpeechBatchResponse> => {
  try {
    const response = await del<DeleteSpeechBatchResponse>('/scene/speech/batch', {
      data: request
    });
    return response;
  } catch (error) {
    console.error('批量删除发言失败:', error);
    throw error;
  }
};

/**
 * 处理任务（流式返回）
 * @param jobId 任务ID
 * @param callbacks 回调函数对象
 * @returns Promise<void>
 */
export const processJob = async (
  jobId: number,
  callbacks: {
    onChunk: (chunk: string) => void;
    onComplete: () => void;
    onError: (error: Error) => void;
  }
): Promise<void> => {
  try {
    const { user } = await import('../utils/authStore').then(m => m.useAuthStore.getState());
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/scene/job/${jobId}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user?.token}`,
      },
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            if (data === '[DONE]') {
              callbacks.onComplete();
              return;
            }
            if (data) {
              callbacks.onChunk(data);
            }
          }
        }
      }
      
      // 处理剩余的buffer
      if (buffer.trim()) {
        callbacks.onChunk(buffer);
      }
      
      callbacks.onComplete();
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error('处理任务失败:', error);
    callbacks.onError(error instanceof Error ? error : new Error('处理任务失败'));
  }
};